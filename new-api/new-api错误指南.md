# New-API 错误指南

本文档记录了在部署和使用 New-API 过程中遇到的常见错误及其解决方案。

## 1. 静态资源 404 错误（最常见问题）

### 错误现象
```
GET https://liangliangdamowang.edu.deal/assets/index-CfGQA3r7.js net::ERR_ABORTED 404 (Not Found)
GET https://liangliangdamowang.edu.deal/assets/semi-ui-yRXI6evF.css 404 (Not Found)
GET https://liangliangdamowang.edu.deal/assets/react-core-DtMAePju.js net::ERR_ABORTED 404 (Not Found)
GET https://liangliangdamowang.edu.deal/assets/semi-ui-BN13Wcpw.js net::ERR_ABORTED 404 (Not Found)
GET https://liangliangdamowang.edu.deal/assets/tools-B-TUGtmI.js net::ERR_ABORTED 404 (Not Found)
GET https://liangliangdamowang.edu.deal/assets/react-components-XvIrFW7f.js net::ERR_ABORTED 404 (Not Found)
GET https://liangliangdamowang.edu.deal/assets/i18n-BSxjx2rs.js net::ERR_ABORTED 404 (Not Found)
GET https://liangliangdamowang.edu.deal/logo.png 404 (Not Found)

Refused to apply style from 'https://liangliangdamowang.edu.deal/assets/semi-ui-yRXI6evF.css' because its MIME type ('text/html') is not a supported stylesheet MIME type, and strict MIME checking is enabled.
Refused to apply style from 'https://liangliangdamowang.edu.deal/assets/index-C8L2jEvp.css' because its MIME type ('text/html') is not a supported stylesheet MIME type, and strict MIME checking is enabled.
```

### 错误原因
1. 前端应用的静态资源路径没有正确使用子路径前缀 `/ai/`
2. 浏览器缓存了旧版本的HTML，导致请求错误的资源路径
3. 前端应用在运行时动态改变了基础路径配置
4. CSS文件返回HTML内容（通常是404页面），导致MIME类型错误

### 解决方案

#### 1.1 修复 API 基础 URL
编辑 `web/src/helpers/api.js`：
```javascript
function getApiBaseURL() {
  if (import.meta.env.VITE_REACT_APP_SERVER_URL) {
    return import.meta.env.VITE_REACT_APP_SERVER_URL;
  }
  // 强制使用/ai作为基础路径
  return '/ai';
}
```

#### 1.2 修复 Logo 路径
编辑 `web/src/helpers/utils.js`：
```javascript
export function getLogo() {
  let logo = localStorage.getItem('logo');
  if (!logo) return '/ai/logo.png';  // 修改默认路径
  return logo;
}
```

#### 1.3 修复 HTML 模板
编辑 `web/index.html`：
```html
<link rel="icon" href="/ai/logo.png" />
```

#### 1.4 清除浏览器缓存
```bash
# 建议用户执行以下操作：
# 1. 按 Ctrl+Shift+Delete 打开清除浏览器数据对话框
# 2. 选择"缓存的图片和文件"
# 3. 点击"清除数据"
# 4. 或者使用 Ctrl+F5 强制刷新页面
```

#### 1.5 重新构建并验证
```bash
cd /root/workspace/new-api

# 停止服务
docker-compose down

# 重新构建（无缓存）
docker-compose build --no-cache

# 启动服务
docker-compose up -d

# 等待服务启动
sleep 15

# 验证HTML中的资源路径
echo "=== 检查HTML中的资源路径 ==="
curl -k -H "Accept-Encoding: identity" https://localhost/ai/ | grep -E "(assets|href=|src=)" | head -10

# 测试静态资源
echo "=== 测试静态资源 ==="
curl -k -I https://localhost/ai/logo.png
```

#### 1.6 紧急修复脚本
如果问题持续存在，使用以下脚本快速修复：

```bash
cat > /root/workspace/new-api/fix-static-resources.sh << 'EOF'
#!/bin/bash

echo "=== 修复静态资源路径问题 ==="

cd /root/workspace/new-api

# 1. 修复 API 基础 URL
echo "1. 修复 API 基础 URL..."
sed -i 's/return '\'''\'';//g' web/src/helpers/api.js
sed -i '/检测当前路径/,/return '\'''\''/{//!d}' web/src/helpers/api.js
sed -i 's/检测当前路径.*/强制使用\/ai作为基础路径，因为应用部署在\/ai子路径下/' web/src/helpers/api.js
sed -i '/强制使用/a\  return '\''/ai'\'';' web/src/helpers/api.js

# 2. 修复 Logo 路径
echo "2. 修复 Logo 路径..."
sed -i "s|return '/logo.png'|return '/ai/logo.png'|g" web/src/helpers/utils.js

# 3. 修复 HTML 模板
echo "3. 修复 HTML 模板..."
sed -i 's|href="/logo.png"|href="/ai/logo.png"|g' web/index.html

# 4. 确保 Vite 配置正确
echo "4. 检查 Vite 配置..."
if ! grep -q "base: '/ai/'" web/vite.config.js; then
    echo "修复 Vite 配置..."
    sed -i "s/base: '[^']*'/base: '\/ai\/'/" web/vite.config.js
fi

# 5. 确保 React Router 配置正确
echo "5. 检查 React Router 配置..."
if ! grep -q 'basename="/ai"' web/src/index.js; then
    echo "修复 React Router 配置..."
    sed -i 's/basename="[^"]*"/basename="\/ai"/' web/src/index.js
fi

# 6. 重新构建
echo "6. 重新构建应用..."
docker-compose down
docker-compose build --no-cache
docker-compose up -d

echo "=== 修复完成，等待服务启动 ==="
sleep 20

# 7. 验证修复结果
echo "7. 验证修复结果..."
echo "主页访问："
curl -k -s -o /dev/null -w "状态码: %{http_code}\n" https://localhost/ai/

echo "Logo 图片："
curl -k -s -o /dev/null -w "状态码: %{http_code}\n" https://localhost/ai/logo.png

echo "API 状态："
curl -k -s https://localhost/ai/api/status | jq '.success' 2>/dev/null || echo "API 测试失败"

echo "=== 修复脚本执行完成 ==="
EOF

chmod +x /root/workspace/new-api/fix-static-resources.sh
/root/workspace/new-api/fix-static-resources.sh
```

## 2. API 请求错误和重定向循环

### 错误现象
```
GET https://liangliangdamowang.edu.deal/api/status 404 (Not Found)
GET https://liangliangdamowang.edu.deal/api/notice net::ERR_TOO_MANY_REDIRECTS
GET https://liangliangdamowang.edu.deal/api/home_page_content net::ERR_TOO_MANY_REDIRECTS

W {message: 'Network Error', name: 'AxiosError', code: 'ERR_NETWORK', config: {…}, request: XMLHttpRequest, …}
获取公告失败:
Failed to load resource: net::ERR_TOO_MANY_REDIRECTS
Failed to load resource: net::ERR_TOO_MANY_REDIRECTS
Uncaught (in promise) W
Failed to load resource: the server responded with a status of 404 ()
```

### 错误原因
1. API 请求路径没有正确使用子路径前缀 `/ai/`
2. Nginx 配置导致重定向循环
3. 后端服务没有正确处理子路径下的 API 请求
4. 前端 API 基础 URL 配置错误

### 解决方案

#### 2.1 修复前端 API 基础 URL
```bash
cd /root/workspace/new-api

# 检查当前 API 基础 URL 配置
echo "=== 当前 API 配置 ==="
grep -n "getBaseUrl\|return.*api" web/src/helpers/api.js

# 修复 API 基础 URL
sed -i 's/return '\'''\'';//g' web/src/helpers/api.js
sed -i '/检测当前路径/,/return '\'''\''/{//!d}' web/src/helpers/api.js
sed -i 's/检测当前路径.*/强制使用\/ai作为基础路径，因为应用部署在\/ai子路径下/' web/src/helpers/api.js
sed -i '/强制使用/a\  return '\''/ai'\'';' web/src/helpers/api.js

echo "=== 修复后的 API 配置 ==="
grep -A5 -B5 "getBaseUrl" web/src/helpers/api.js
```

#### 2.2 检查和修复 Nginx 重定向循环
```bash
# 检查 Nginx 配置
echo "=== 检查 Nginx 配置 ==="
docker exec nginx-proxy nginx -t

# 检查是否存在重定向循环
echo "=== 测试重定向 ==="
curl -k -I -L --max-redirs 5 https://localhost/ai/api/status

# 如果存在重定向循环，修复 Nginx 配置
cat > /tmp/fix-nginx-redirects.conf << 'EOF'
# 修复重定向循环的 location 配置
location /ai/api/ {
    include /etc/nginx/includes/proxy-common.conf;
    rewrite ^/ai/(.*)$ /$1 break;
    proxy_pass http://new-api;
    proxy_set_header X-Forwarded-Prefix /ai;
    limit_req zone=api burst=10 nodelay;
}

location /ai/ {
    include /etc/nginx/includes/proxy-common.conf;
    rewrite ^/ai/(.*)$ /$1 break;
    proxy_pass http://new-api/;
    proxy_set_header X-Forwarded-Prefix /ai;
    limit_req zone=api burst=10 nodelay;
}
EOF

echo "建议的 Nginx 配置已保存到 /tmp/fix-nginx-redirects.conf"
```

#### 2.3 验证 API 修复
```bash
# 重新构建并测试
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# 等待服务启动
sleep 20

# 测试 API 端点
echo "=== 测试 API 端点 ==="
echo "状态 API："
curl -k -s https://localhost/ai/api/status | jq '.' || echo "状态 API 失败"

echo "公告 API："
curl -k -s https://localhost/ai/api/notice | jq '.' || echo "公告 API 失败"

echo "主页内容 API："
curl -k -s https://localhost/ai/api/home_page_content | jq '.' || echo "主页内容 API 失败"
```

## 3. Content Security Policy (CSP) 错误

### 错误现象
```
Refused to load the image 'blob:https://liangliangdamowang.edu.deal/da4f7856-a78f-4c11-b197-cd0d996e0d62' because it violates the following Content Security Policy directive: "img-src 'self' data: https:".
```

### 错误原因
CSP 策略阻止了 blob URL 的图片加载。

### 解决方案

#### 3.1 修复 CSP 配置
```bash
# 检查当前 CSP 配置
echo "=== 检查 CSP 配置 ==="
grep -r "Content-Security-Policy" /root/workspace/new-api/

# 修复 CSP 配置以允许 blob URLs
# 在 Nginx 配置中添加或修改 CSP 头
cat >> /tmp/csp-fix.conf << 'EOF'
# 修复 CSP 配置
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https: wss:; frame-src 'self'; object-src 'none';" always;
EOF

echo "CSP 修复配置已保存到 /tmp/csp-fix.conf"
echo "请将此配置添加到 Nginx 的 location 块中"
```

## 4. Nginx 配置错误

### 错误现象
```
nginx: [emerg] "location" directive is not allowed here
```

### 错误原因
Nginx 配置中存在嵌套的 location 块。

### 解决方案

#### 2.1 修复 location 块顺序
确保 location 块不嵌套，按优先级排序：

```nginx
# 特殊处理登录接口 - 最高优先级
location /ai/api/user/login {
    include /etc/nginx/includes/proxy-common.conf;
    rewrite ^/ai/(.*)$ /$1 break;
    proxy_pass http://new-api;
    proxy_set_header X-Forwarded-Prefix /ai;
    limit_req zone=login burst=3 nodelay;
}

# 特殊处理静态资源
location ~* /ai/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    include /etc/nginx/includes/proxy-common.conf;
    rewrite ^/ai/(.*)$ /$1 break;
    proxy_pass http://new-api;
    proxy_set_header X-Forwarded-Prefix /ai;
    expires 1d;
    add_header Cache-Control "public, immutable";
}

# New-API 路径配置 - 核心子路径访问
location /ai/ {
    include /etc/nginx/includes/proxy-common.conf;
    rewrite ^/ai/(.*)$ /$1 break;
    proxy_pass http://new-api/;
    proxy_set_header X-Forwarded-Prefix /ai;
    limit_req zone=api burst=10 nodelay;
    limit_conn conn_limit_per_ip 10;
}
```

#### 2.2 重新加载配置
```bash
cd /root/workspace/shared/nginx
docker-compose exec nginx nginx -t
docker-compose exec nginx nginx -s reload
```

## 3. API 请求 404 错误

### 错误现象
```
GET https://domain.com/ai/api/status 404 (Not Found)
```

### 错误原因
1. new-api 服务不支持 HEAD 请求
2. nginx 代理配置问题

### 解决方案

#### 3.1 检查服务状态
```bash
cd /root/workspace/new-api
docker-compose ps
docker-compose logs new-api
```

#### 3.2 测试直接访问
```bash
# 测试 GET 请求（应该成功）
curl -k -s https://localhost/ai/api/status | jq '.success'

# 测试 HEAD 请求（可能失败）
curl -k -I https://localhost/ai/api/status
```

#### 3.3 检查 nginx 代理配置
确保 proxy-common.conf 正确配置：
```nginx
proxy_set_header Host $host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host $server_name;
proxy_set_header X-Forwarded-Port $server_port;
```

## 4. 容器启动失败

### 错误现象
```
ERROR: for new-api  Cannot start service new-api: driver failed programming external connectivity
```

### 错误原因
端口冲突或网络配置问题。

### 解决方案

#### 4.1 检查端口占用
```bash
netstat -tlnp | grep :3000
netstat -tlnp | grep :80
netstat -tlnp | grep :443
```

#### 4.2 清理 Docker 网络
```bash
docker network prune
docker system prune
```

#### 4.3 重新启动服务
```bash
docker-compose down
docker-compose up -d
```

## 5. SSL 证书问题

### 错误现象
```
SSL_ERROR_BAD_CERT_DOMAIN
```

### 错误原因
SSL 证书域名不匹配或证书文件路径错误。

### 解决方案

#### 5.1 检查证书文件
```bash
ls -la /root/workspace/shared/nginx/ssl/
openssl x509 -in /root/workspace/shared/nginx/ssl/certificate.crt -text -noout | grep "Subject:"
```

#### 5.2 检查证书权限
```bash
chmod 644 /root/workspace/shared/nginx/ssl/certificate.crt
chmod 600 /root/workspace/shared/nginx/ssl/private.key
```

#### 5.3 验证 nginx 配置
```bash
docker-compose exec nginx nginx -t
```

## 6. 数据库连接失败

### 错误现象
```
failed to connect to database: dial tcp: lookup mysql on 127.0.0.11:53: no such host
```

### 错误原因
Docker 网络配置问题或数据库服务未启动。

### 解决方案

#### 6.1 检查数据库服务
```bash
docker-compose ps mysql
docker-compose logs mysql
```

#### 6.2 检查网络连接
```bash
docker exec new-api ping mysql
```

#### 6.3 检查环境变量
```bash
docker exec new-api env | grep SQL_DSN
```

## 7. 前端页面空白

### 错误现象
访问页面显示空白，控制台有 JavaScript 错误。

### 错误原因
1. 静态资源加载失败
2. JavaScript 路径配置错误
3. React Router 配置问题

### 解决方案

#### 7.1 检查静态资源
```bash
curl -k -I https://localhost/ai/assets/index-xxx.js
```

#### 7.2 检查 React Router 配置
确保 `web/src/index.js` 中：
```javascript
<BrowserRouter basename="/ai">
```

#### 7.3 检查 Vite 配置
确保 `web/vite.config.js` 中：
```javascript
export default defineConfig({
  base: '/ai/',
  // ...
});
```

## 8. CSP 策略阻止资源加载

### 错误现象
```
Refused to load the image 'blob:https://domain.com/xxx' because it violates the following Content Security Policy directive
```

### 错误原因
Content Security Policy 配置过于严格。

### 解决方案

#### 8.1 修改 CSP 配置
编辑 `shared/nginx/includes/security-headers.conf`：
```nginx
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https: blob:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'self';" always;
```

#### 8.2 重新加载 nginx
```bash
docker-compose exec nginx nginx -s reload
```

## 9. 构建失败

### 错误现象
```
ERROR: failed to solve: process "/bin/sh -c bun run build" did not complete successfully
```

### 错误原因
1. 依赖安装失败
2. 内存不足
3. 网络问题

### 解决方案

#### 9.1 清理构建缓存
```bash
docker system prune -f
docker builder prune -f
```

#### 9.2 增加构建内存
```bash
docker-compose build --no-cache --memory=2g
```

#### 9.3 检查磁盘空间
```bash
df -h
docker system df
```

## 10. 重定向循环

### 错误现象
```
ERR_TOO_MANY_REDIRECTS
```

### 错误原因
nginx 配置中存在重定向循环。

### 解决方案

#### 10.1 检查重定向配置
确保只有一个重定向规则：
```nginx
location = /ai {
    return 301 /ai/;
}
```

#### 10.2 检查代理配置
确保 rewrite 规则正确：
```nginx
rewrite ^/ai/(.*)$ /$1 break;
```

## 故障排除工具

### 日志查看命令
```bash
# Nginx 日志
docker-compose -f /root/workspace/shared/nginx/docker-compose.yml logs nginx

# New-API 日志
docker-compose -f /root/workspace/new-api/docker-compose.yml logs new-api

# 系统日志
tail -f /root/workspace/shared/nginx/logs/error/error.log
```

### 网络测试命令
```bash
# 测试容器间通信
docker exec nginx-proxy ping new-api

# 测试端口连接
telnet localhost 3000
telnet localhost 443

# 测试 DNS 解析
nslookup liangliangdamowang.edu.deal
```

### 配置验证命令
```bash
# 验证 nginx 配置
docker-compose exec nginx nginx -t

# 验证 SSL 证书
openssl s_client -connect localhost:443 -servername liangliangdamowang.edu.deal

# 验证服务健康
curl -k -s https://localhost/ai/api/status | jq '.success'
```

## 预防措施

1. **定期备份**：备份数据库和配置文件
2. **监控日志**：设置日志监控和告警
3. **测试环境**：在测试环境验证更改
4. **版本控制**：使用 Git 管理配置文件
5. **文档更新**：及时更新部署文档

## 紧急恢复

如果遇到严重问题，可以使用以下紧急恢复步骤：

```bash
# 1. 停止所有服务
docker-compose -f /root/workspace/new-api/docker-compose.yml down
docker-compose -f /root/workspace/shared/nginx/docker-compose.yml down

# 2. 恢复到最近的备份
# (参考更新指导中的回滚方案)

# 3. 重新启动服务
docker-compose -f /root/workspace/new-api/docker-compose.yml up -d
docker-compose -f /root/workspace/shared/nginx/docker-compose.yml up -d

# 4. 验证服务状态
curl -k -s https://localhost/ai/api/status | jq '.success'
```