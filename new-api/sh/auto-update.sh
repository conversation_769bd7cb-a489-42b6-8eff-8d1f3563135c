#!/bin/bash

# New-API 自动更新脚本
# 功能：拉取最新代码、应用补丁、重新构建和部署
# 作者：AI Assistant
# 版本：v2.0
# 日期：$(date +%Y-%m-%d)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
NEW_API_DIR="/root/workspace/new-api"
NGINX_DIR="/root/workspace/shared/nginx"
BACKUP_DIR="/root/workspace/backup/$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/root/workspace/new-api/update.log"

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查必要命令
    for cmd in git docker docker-compose curl jq; do
        if ! command -v $cmd &> /dev/null; then
            log_error "$cmd 命令未找到，请先安装"
            exit 1
        fi
    done
    
    # 检查目录
    if [ ! -d "$NEW_API_DIR" ]; then
        log_error "New-API 目录不存在: $NEW_API_DIR"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 创建完整备份
create_backup() {
    log_info "创建完整备份到 $BACKUP_DIR..."
    mkdir -p "$BACKUP_DIR"
    
    cd "$NEW_API_DIR"
    
    # 备份数据库
    if docker ps | grep -q mysql; then
        log_info "备份数据库..."
        docker exec mysql mysqldump -uroot -p123456 new-api > "$BACKUP_DIR/database_backup.sql" 2>/dev/null || log_warning "数据库备份失败"
    else
        log_warning "MySQL 容器未运行，跳过数据库备份"
    fi
    
    # 备份配置文件
    log_info "备份配置文件..."
    mkdir -p "$BACKUP_DIR/nginx_conf"
    cp -r "$NGINX_DIR/conf.d/" "$BACKUP_DIR/nginx_conf/" 2>/dev/null || log_warning "Nginx 配置备份失败"
    cp -r "$NGINX_DIR/includes/" "$BACKUP_DIR/nginx_conf/" 2>/dev/null || log_warning "Nginx includes 备份失败"
    
    # 备份应用数据
    log_info "备份应用数据..."
    cp -r "$NEW_API_DIR/data/" "$BACKUP_DIR/new-api_data/" 2>/dev/null || log_warning "应用数据备份失败"
    
    # 备份关键源码文件
    log_info "备份关键源码文件..."
    mkdir -p "$BACKUP_DIR/source_files"
    cp "$NEW_API_DIR/web/src/helpers/api.js" "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "api.js 备份失败"
    cp "$NEW_API_DIR/web/src/helpers/utils.js" "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "utils.js 备份失败"
    cp "$NEW_API_DIR/web/index.html" "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "index.html 备份失败"
    cp "$NEW_API_DIR/web/vite.config.js" "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "vite.config.js 备份失败"
    cp "$NEW_API_DIR/web/src/index.js" "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "index.js 备份失败"
    cp "$NEW_API_DIR/docker-compose.yml" "$BACKUP_DIR/source_files/" 2>/dev/null || log_warning "docker-compose.yml 备份失败"
    
    # 备份补丁文件
    cp -r "$NEW_API_DIR/patches/" "$BACKUP_DIR/" 2>/dev/null || log_warning "补丁目录备份失败"
    
    # 记录当前状态
    log_info "记录当前状态..."
    git log --oneline -10 > "$BACKUP_DIR/git_history.txt" 2>/dev/null || log_warning "Git 历史记录失败"
    docker images | grep new-api > "$BACKUP_DIR/docker_images.txt" 2>/dev/null || log_warning "Docker 镜像记录失败"
    docker-compose ps > "$BACKUP_DIR/docker_status.txt" 2>/dev/null || log_warning "Docker 状态记录失败"
    
    log_success "备份完成: $BACKUP_DIR"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    cd "$NEW_API_DIR"
    docker-compose down || log_warning "New-API 服务停止失败"
    
    cd "$NGINX_DIR"
    docker-compose down || log_warning "Nginx 服务停止失败"
    
    log_success "服务已停止"
}

# 拉取最新代码
pull_latest_code() {
    log_info "拉取最新代码..."
    
    cd "$NEW_API_DIR"
    
    # 保存当前分支
    CURRENT_BRANCH=$(git branch --show-current)
    log_info "当前分支: $CURRENT_BRANCH"
    
    # 检查是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        log_warning "检测到未提交的更改，将暂存..."
        git stash push -m "Auto-update stash $(date)"
    fi
    
    # 获取最新代码
    git fetch origin || {
        log_error "获取远程代码失败"
        exit 1
    }
    
    # 显示可用更新
    log_info "可用更新:"
    git log --oneline HEAD..origin/main | head -10 || log_info "没有新的更新"
    
    # 合并最新代码
    if git merge origin/main; then
        log_success "代码更新成功"
    else
        log_error "代码合并失败，可能存在冲突"
        git status
        exit 1
    fi
}

# 应用子路径补丁
apply_subpath_patches() {
    log_info "应用子路径补丁..."
    
    cd "$NEW_API_DIR"
    
    # 1. 修复 API 基础 URL
    log_info "修复 API 基础 URL..."
    if [ -f "web/src/helpers/api.js" ]; then
        # 清理旧的返回语句
        sed -i 's/return '\'''\'';//g' web/src/helpers/api.js
        
        # 查找并替换 getApiBaseURL 函数中的返回逻辑
        if grep -q "检测当前路径" web/src/helpers/api.js; then
            sed -i '/检测当前路径/,/return '\'''\''/{//!d}' web/src/helpers/api.js
            sed -i 's/检测当前路径.*/强制使用\/ai作为基础路径，因为应用部署在\/ai子路径下/' web/src/helpers/api.js
            sed -i '/强制使用/a\  return '\''/ai'\'';' web/src/helpers/api.js
        else
            # 如果没有找到特定注释，尝试直接替换
            sed -i "s|return '';|return '/ai';|g" web/src/helpers/api.js
            sed -i "s|return \"\";|return '/ai';|g" web/src/helpers/api.js
        fi
        log_success "API 基础 URL 修复完成"
    else
        log_warning "api.js 文件不存在"
    fi
    
    # 2. 修复 Logo 路径
    log_info "修复 Logo 路径..."
    if [ -f "web/src/helpers/utils.js" ]; then
        sed -i "s|return '/logo.png'|return '/ai/logo.png'|g" web/src/helpers/utils.js
        sed -i 's|return "/logo.png"|return "/ai/logo.png"|g' web/src/helpers/utils.js
        log_success "Logo 路径修复完成"
    else
        log_warning "utils.js 文件不存在"
    fi
    
    # 3. 修复 HTML 模板
    log_info "修复 HTML 模板..."
    if [ -f "web/index.html" ]; then
        sed -i 's|href="/logo.png"|href="/ai/logo.png"|g' web/index.html
        sed -i 's|src="/logo.png"|src="/ai/logo.png"|g' web/index.html
        log_success "HTML 模板修复完成"
    else
        log_warning "index.html 文件不存在"
    fi
    
    # 4. 修复 Vite 配置
    log_info "修复 Vite 配置..."
    if [ -f "web/vite.config.js" ]; then
        if ! grep -q "base: '/ai/'" web/vite.config.js; then
            sed -i "s/base: '[^']*'/base: '\/ai\/'/" web/vite.config.js
            log_success "Vite 配置修复完成"
        else
            log_info "Vite 配置已正确"
        fi
    else
        log_warning "vite.config.js 文件不存在"
    fi
    
    # 5. 修复 React Router 配置
    log_info "修复 React Router 配置..."
    if [ -f "web/src/index.js" ]; then
        if ! grep -q 'basename="/ai"' web/src/index.js; then
            sed -i 's/basename="[^"]*"/basename="\/ai"/' web/src/index.js
            log_success "React Router 配置修复完成"
        else
            log_info "React Router 配置已正确"
        fi
    else
        log_warning "index.js 文件不存在"
    fi
    
    log_success "所有补丁应用完成"
}

# 验证配置
verify_configuration() {
    log_info "验证配置..."
    
    cd "$NEW_API_DIR"
    
    local config_ok=true
    
    # 检查 API 基础 URL
    if grep -q "return '/ai'" web/src/helpers/api.js 2>/dev/null; then
        log_success "✓ API 基础 URL 配置正确"
    else
        log_error "✗ API 基础 URL 配置错误"
        config_ok=false
    fi
    
    # 检查 Logo 路径
    if grep -q "/ai/logo.png" web/src/helpers/utils.js 2>/dev/null; then
        log_success "✓ Logo 路径配置正确"
    else
        log_error "✗ Logo 路径配置错误"
        config_ok=false
    fi
    
    # 检查 HTML 模板
    if grep -q "/ai/logo.png" web/index.html 2>/dev/null; then
        log_success "✓ HTML 模板配置正确"
    else
        log_error "✗ HTML 模板配置错误"
        config_ok=false
    fi
    
    # 检查 Vite 配置
    if grep -q "base: '/ai/'" web/vite.config.js 2>/dev/null; then
        log_success "✓ Vite 配置正确"
    else
        log_error "✗ Vite 配置错误"
        config_ok=false
    fi
    
    # 检查 React Router 配置
    if grep -q 'basename="/ai"' web/src/index.js 2>/dev/null; then
        log_success "✓ React Router 配置正确"
    else
        log_error "✗ React Router 配置错误"
        config_ok=false
    fi
    
    if [ "$config_ok" = false ]; then
        log_error "配置验证失败，请检查上述错误"
        exit 1
    fi
    
    log_success "配置验证通过"
}

# 重新构建和启动服务
rebuild_and_start() {
    log_info "重新构建和启动服务..."

    # 清理 Docker 缓存
    log_info "清理 Docker 缓存..."
    docker system prune -f || log_warning "Docker 缓存清理失败"

    # 构建 New-API
    log_info "构建 New-API..."
    cd "$NEW_API_DIR"
    if docker-compose build --no-cache --pull; then
        log_success "New-API 构建成功"
    else
        log_error "New-API 构建失败"
        exit 1
    fi

    # 启动 New-API
    log_info "启动 New-API..."
    if docker-compose up -d; then
        log_success "New-API 启动成功"
    else
        log_error "New-API 启动失败"
        exit 1
    fi

    # 等待服务启动
    log_info "等待 New-API 服务启动..."
    sleep 20

    # 启动 Nginx
    log_info "启动 Nginx..."
    cd "$NGINX_DIR"
    if docker-compose up -d; then
        log_success "Nginx 启动成功"
    else
        log_error "Nginx 启动失败"
        exit 1
    fi

    # 等待 Nginx 启动
    log_info "等待 Nginx 服务启动..."
    sleep 10

    log_success "所有服务启动完成"
}

# 验证更新结果
verify_update() {
    log_info "验证更新结果..."

    local test_passed=0
    local test_total=0

    # 测试主页访问
    log_info "测试主页访问..."
    ((test_total++))
    if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/ | grep -q "200"; then
        log_success "✓ 主页访问正常"
        ((test_passed++))
    else
        log_error "✗ 主页访问失败"
    fi

    # 测试 API 状态
    log_info "测试 API 状态..."
    ((test_total++))
    if curl -k -s https://localhost/ai/api/status | jq -e '.success' >/dev/null 2>&1; then
        log_success "✓ API 状态正常"
        ((test_passed++))
    else
        log_error "✗ API 状态异常"
    fi

    # 测试 Logo 图片
    log_info "测试 Logo 图片..."
    ((test_total++))
    if curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ai/logo.png | grep -q "200"; then
        log_success "✓ Logo 图片正常"
        ((test_passed++))
    else
        log_error "✗ Logo 图片失败"
    fi

    # 测试静态资源
    log_info "测试静态资源..."
    ((test_total++))
    JS_FILE=$(curl -k -s https://localhost/ai/ | grep -o '/ai/assets/index-[^"]*\.js' | head -1)
    if [ -n "$JS_FILE" ] && curl -k -s -o /dev/null -w "%{http_code}" "https://localhost$JS_FILE" | grep -q "200"; then
        log_success "✓ 静态资源正常"
        ((test_passed++))
    else
        log_error "✗ 静态资源失败"
    fi

    # 测试关键 API 端点
    for endpoint in "notice" "home_page_content"; do
        log_info "测试 /ai/api/$endpoint..."
        ((test_total++))
        if curl -k -s -o /dev/null -w "%{http_code}" "https://localhost/ai/api/$endpoint" | grep -qE "200|404"; then
            log_success "✓ API $endpoint 响应正常"
            ((test_passed++))
        else
            log_error "✗ API $endpoint 响应异常"
        fi
    done

    # 显示测试结果
    log_info "测试结果: $test_passed/$test_total 通过"

    if [ $test_passed -eq $test_total ]; then
        log_success "所有测试通过，更新成功！"
        return 0
    else
        log_warning "部分测试失败，请检查服务状态"
        return 1
    fi
}

# 显示服务状态
show_status() {
    log_info "显示服务状态..."

    echo "=== Docker 容器状态 ==="
    docker-compose -f "$NEW_API_DIR/docker-compose.yml" ps
    echo
    docker-compose -f "$NGINX_DIR/docker-compose.yml" ps

    echo
    echo "=== 服务端口监听 ==="
    netstat -tlnp | grep -E ":80|:443|:3000" || echo "未找到相关端口监听"

    echo
    echo "=== 最近日志 ==="
    echo "New-API 日志:"
    docker-compose -f "$NEW_API_DIR/docker-compose.yml" logs --tail=5 new-api 2>/dev/null || echo "无法获取 New-API 日志"

    echo
    echo "Nginx 日志:"
    docker-compose -f "$NGINX_DIR/docker-compose.yml" logs --tail=5 nginx-proxy 2>/dev/null || echo "无法获取 Nginx 日志"
}

# 清理函数
cleanup() {
    log_info "执行清理..."
    # 这里可以添加清理逻辑
}

# 主函数
main() {
    echo "========================================"
    echo "    New-API 自动更新脚本 v2.0"
    echo "========================================"
    echo "开始时间: $(date)"
    echo "备份目录: $BACKUP_DIR"
    echo "日志文件: $LOG_FILE"
    echo "========================================"

    # 设置错误处理
    trap cleanup EXIT

    # 记录日志
    exec > >(tee -a "$LOG_FILE")
    exec 2>&1

    # 执行更新流程
    check_dependencies
    create_backup
    stop_services
    pull_latest_code
    apply_subpath_patches
    verify_configuration
    rebuild_and_start

    # 验证更新结果
    if verify_update; then
        log_success "更新完成！"
        show_status

        echo
        echo "========================================"
        echo "更新成功完成！"
        echo "备份目录: $BACKUP_DIR"
        echo "访问地址: https://your-domain.com/ai/"
        echo "结束时间: $(date)"
        echo "========================================"
    else
        log_warning "更新完成但存在问题，请检查服务状态"
        show_status
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
