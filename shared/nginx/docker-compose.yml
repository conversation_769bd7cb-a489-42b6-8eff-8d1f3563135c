version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 挂载nginx主配置文件
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      # 挂载站点配置目录
      - ./conf.d:/etc/nginx/conf.d:ro
      # 挂载SSL证书目录
      - ./ssl:/etc/nginx/ssl:ro
      # 挂载包含文件目录
      - ./includes:/etc/nginx/includes:ro
      # 挂载日志目录
      - ./logs/access:/var/log/nginx/access:rw
      - ./logs/error:/var/log/nginx/error:rw
      # 挂载静态文件目录
      - ./www:/var/www:ro
    networks:
      - nginx-network
      - new-api_default  # 连接到new-api网络
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

    environment:
      - TZ=Asia/Shanghai



networks:
  nginx-network:
    driver: bridge
  new-api_default:
    external: true  # 使用外部已存在的new-api网络

volumes:
  nginx_logs:
    driver: local
