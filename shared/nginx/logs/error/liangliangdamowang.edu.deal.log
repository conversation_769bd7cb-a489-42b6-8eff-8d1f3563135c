2025/07/26 20:39:14 [error] 20#20: *6 directory index of "/var/www/html/" is forbidden, client: **********, server: liangliangdamowang.edu.deal, request: "HEAD / HTTP/2.0", host: "localhost"
2025/07/26 20:55:16 [error] 20#20: *61 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/26 20:55:17 [error] 20#20: *62 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "POST / HTTP/1.1", host: "************"
2025/07/26 21:02:55 [error] 285#285: *81 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************/"
2025/07/26 21:13:21 [error] 512#512: *99 directory index of "/var/www/html/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************:80/"
2025/07/26 21:21:20 [error] 512#512: *137 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/26 21:39:29 [error] 512#512: *209 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/26 22:14:37 [error] 512#512: *349 directory index of "/var/www/html/" is forbidden, client: ***************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********/"
2025/07/26 22:15:23 [error] 512#512: *350 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************/"
2025/07/26 22:16:52 [error] 512#512: *351 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************:80"
2025/07/26 22:24:41 [error] 512#512: *352 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********:80"
2025/07/26 22:25:02 [error] 512#512: *354 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://************:80"
2025/07/26 22:37:02 [error] 512#512: *361 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/26 22:42:36 [error] 512#512: *389 directory index of "/var/www/html/" is forbidden, client: ************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://***********/"
2025/07/26 23:07:30 [error] 512#512: *410 access forbidden by rule, client: ************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/26 23:29:26 [error] 20#20: *19 access forbidden by rule, client: ************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/26 23:39:45 [error] 20#20: *80 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://liangliangdamowang.edu.deal"
2025/07/26 23:45:15 [error] 20#20: *86 directory index of "/var/www/html/" is forbidden, client: *************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal", referrer: "http://**************"
2025/07/26 23:46:14 [error] 20#20: *87 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/26 23:55:03 [error] 20#20: *92 directory index of "/var/www/html/" is forbidden, client: ************, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "liangliangdamowang.edu.deal"
2025/07/27 00:02:20 [error] 20#20: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "PUT /ai/api/channel/ HTTP/2.0", upstream: "http://**********:3000/api/channel/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/27 00:02:20 [error] 20#20: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "PUT /ai/api/channel/ HTTP/2.0", upstream: "http://**********:3000/api/channel/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/27 00:02:23 [error] 20#20: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /ai/console/channel HTTP/2.0", upstream: "http://**********:3000/console/channel", host: "liangliangdamowang.edu.deal"
2025/07/27 00:02:27 [error] 20#20: *142 connect() failed (113: Host is unreachable) while connecting to upstream, client: ***************, server: liangliangdamowang.edu.deal, request: "GET /ai/ HTTP/2.0", upstream: "http://**********:3000/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/27 00:02:52 [error] 20#20: *149 directory index of "/var/www/html/" is forbidden, client: ***********, server: liangliangdamowang.edu.deal, request: "GET / HTTP/1.1", host: "************"
2025/07/27 00:03:37 [error] 20#20: *151 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /ai/console/channel HTTP/2.0", upstream: "http://**********:3000/console/channel", host: "liangliangdamowang.edu.deal"
2025/07/27 00:03:40 [error] 20#20: *151 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /ai/ HTTP/2.0", upstream: "http://**********:3000/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/console/channel"
2025/07/27 00:03:46 [error] 20#20: *151 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /ai/ HTTP/2.0", upstream: "http://**********:3000/", host: "liangliangdamowang.edu.deal"
2025/07/27 00:03:49 [error] 20#20: *151 connect() failed (113: Host is unreachable) while connecting to upstream, client: **************, server: liangliangdamowang.edu.deal, request: "GET /ai/ HTTP/2.0", upstream: "http://**********:3000/", host: "liangliangdamowang.edu.deal", referrer: "https://liangliangdamowang.edu.deal/ai/"
2025/07/27 00:04:39 [error] 20#20: *157 access forbidden by rule, client: **************, server: liangliangdamowang.edu.deal, request: "GET /.env HTTP/1.1", host: "************"
2025/07/27 00:04:39 [error] 20#20: *158 directory index of "/var/www/html/" is forbidden, client: **************, server: liangliangdamowang.edu.deal, request: "POST / HTTP/1.1", host: "************"
2025/07/27 00:14:25 [error] 20#20: *282 limiting connections by zone "conn_limit_per_ip", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting connections by zone "conn_limit_per_ip", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting connections by zone "conn_limit_per_ip", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting requests, excess: 10.350 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting requests, excess: 10.195 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting requests, excess: 10.060 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting connections by zone "conn_limit_per_ip", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:25 [error] 20#20: *282 limiting requests, excess: 10.825 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:26 [error] 20#20: *282 limiting requests, excess: 10.740 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:26 [error] 20#20: *282 limiting requests, excess: 10.640 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:26 [error] 20#20: *282 limiting requests, excess: 10.565 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
2025/07/27 00:14:26 [error] 20#20: *282 limiting requests, excess: 10.510 by zone "api", client: **************, server: liangliangdamowang.edu.deal, request: "POST /ai/v1/chat/completions HTTP/2.0", host: "liangliangdamowang.edu.deal"
